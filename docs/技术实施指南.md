# 数据可视化工具 - 技术实施指南

## 📋 文档概述
- **文档目的**: 为开发团队提供详细的技术实施指导
- **适用对象**: 前端工程师、后端工程师、全栈工程师
- **更新频率**: 随项目进展持续更新

## 🚀 快速开始

### 环境要求
```bash
# Node.js版本要求
Node.js >= 18.0.0
npm >= 9.0.0

# 数据库要求
PostgreSQL >= 15.0
Redis >= 7.0

# 开发工具
Docker >= 24.0
Git >= 2.40
```

### 项目初始化
```bash
# 1. 克隆项目仓库
git clone https://github.com/company/data-viz-tool.git
cd data-viz-tool

# 2. 安装依赖
npm run install:all

# 3. 环境配置
cp .env.example .env
# 编辑 .env 文件配置数据库连接等

# 4. 数据库初始化
npm run db:setup

# 5. 启动开发服务
npm run dev
```

## 🎨 前端开发指南

### 项目结构详解
```
frontend/
├── public/                 # 静态资源
├── src/
│   ├── components/         # 可复用组件
│   │   ├── common/        # 通用组件
│   │   │   ├── Button/    # 按钮组件
│   │   │   ├── Modal/     # 弹窗组件
│   │   │   └── Table/     # 表格组件
│   │   ├── charts/        # 图表组件
│   │   │   ├── BaseChart/ # 图表基类
│   │   │   ├── BarChart/  # 柱状图
│   │   │   ├── LineChart/ # 折线图
│   │   │   └── PieChart/  # 饼图
│   │   └── layout/        # 布局组件
│   │       ├── Header/    # 页头
│   │       ├── Sidebar/   # 侧边栏
│   │       └── Footer/    # 页脚
│   ├── pages/             # 页面组件
│   │   ├── DataManagement/# 数据管理页面
│   │   ├── ChartEditor/   # 图表编辑页面
│   │   ├── Dashboard/     # 仪表板页面
│   │   └── UserCenter/    # 用户中心
│   ├── store/             # 状态管理
│   │   ├── slices/        # Redux切片
│   │   ├── api/           # API切片
│   │   └── index.ts       # Store配置
│   ├── services/          # 服务层
│   │   ├── api/           # API客户端
│   │   ├── auth/          # 认证服务
│   │   └── websocket/     # WebSocket服务
│   ├── utils/             # 工具函数
│   ├── types/             # TypeScript类型定义
│   ├── constants/         # 常量定义
│   └── assets/            # 静态资源
├── package.json
├── vite.config.ts         # Vite配置
├── tsconfig.json          # TypeScript配置
└── tailwind.config.js     # Tailwind CSS配置
```

### 组件开发规范

#### 1. 组件命名规范
```typescript
// 组件文件命名: PascalCase
// 文件: src/components/common/DataTable/index.tsx

import React from 'react';
import { DataTableProps } from './types';
import './styles.scss';

export const DataTable: React.FC<DataTableProps> = ({
  data,
  columns,
  loading = false,
  onRowClick,
  ...props
}) => {
  // 组件实现
  return (
    <div className="data-table" {...props}>
      {/* 组件内容 */}
    </div>
  );
};

export default DataTable;
```

#### 2. 类型定义规范
```typescript
// src/components/common/DataTable/types.ts
export interface DataTableColumn {
  key: string;
  title: string;
  dataIndex: string;
  width?: number;
  render?: (value: any, record: any, index: number) => React.ReactNode;
}

export interface DataTableProps {
  data: any[];
  columns: DataTableColumn[];
  loading?: boolean;
  pagination?: boolean;
  onRowClick?: (record: any, index: number) => void;
  className?: string;
}
```

#### 3. 样式规范
```scss
// src/components/common/DataTable/styles.scss
.data-table {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  &__header {
    background-color: #fafafa;
    font-weight: 600;
  }

  &__row {
    &:hover {
      background-color: #f5f5f5;
    }

    &--selected {
      background-color: #e6f7ff;
    }
  }
}
```

### 状态管理实施

#### Redux Toolkit 配置
```typescript
// src/store/index.ts
import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { apiSlice } from './api';
import authSlice from './slices/authSlice';
import dataSlice from './slices/dataSlice';
import chartsSlice from './slices/chartsSlice';
import dashboardSlice from './slices/dashboardSlice';

export const store = configureStore({
  reducer: {
    api: apiSlice.reducer,
    auth: authSlice,
    data: dataSlice,
    charts: chartsSlice,
    dashboard: dashboardSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }).concat(apiSlice.middleware),
});

setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
```

#### API 切片示例
```typescript
// src/store/api/dataApi.ts
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { Dataset, CreateDatasetRequest } from '../../types/data';

export const dataApi = createApi({
  reducerPath: 'dataApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/v1/data',
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as RootState).auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['Dataset'],
  endpoints: (builder) => ({
    getDatasets: builder.query<Dataset[], void>({
      query: () => '/datasets',
      providesTags: ['Dataset'],
    }),
    createDataset: builder.mutation<Dataset, CreateDatasetRequest>({
      query: (data) => ({
        url: '/datasets',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Dataset'],
    }),
    uploadFile: builder.mutation<{ fileId: string }, FormData>({
      query: (formData) => ({
        url: '/upload',
        method: 'POST',
        body: formData,
      }),
    }),
  }),
});

export const {
  useGetDatasetsQuery,
  useCreateDatasetMutation,
  useUploadFileMutation,
} = dataApi;
```

### 图表组件开发

#### ECharts 封装示例
```typescript
// src/components/charts/BaseChart/index.tsx
import React, { useRef, useEffect } from 'react';
import * as echarts from 'echarts';
import { BaseChartProps } from './types';

export const BaseChart: React.FC<BaseChartProps> = ({
  option,
  width = '100%',
  height = '400px',
  theme = 'default',
  onChartReady,
  onEvents,
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (chartRef.current) {
      // 初始化图表
      chartInstance.current = echarts.init(chartRef.current, theme);
      
      // 绑定事件
      if (onEvents) {
        Object.entries(onEvents).forEach(([eventName, handler]) => {
          chartInstance.current?.on(eventName, handler);
        });
      }

      // 图表准备完成回调
      onChartReady?.(chartInstance.current);
    }

    return () => {
      chartInstance.current?.dispose();
    };
  }, []);

  useEffect(() => {
    if (chartInstance.current && option) {
      chartInstance.current.setOption(option, true);
    }
  }, [option]);

  useEffect(() => {
    const handleResize = () => {
      chartInstance.current?.resize();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div
      ref={chartRef}
      style={{ width, height }}
      className="base-chart"
    />
  );
};
```

#### 图表配置生成器
```typescript
// src/utils/chartConfig.ts
import { ChartType, ChartData, ChartConfig } from '../types/chart';

export class ChartConfigGenerator {
  static generateBarChart(data: ChartData, config: Partial<ChartConfig>) {
    return {
      title: {
        text: config.title || '柱状图',
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      xAxis: {
        type: 'category',
        data: data.categories,
      },
      yAxis: {
        type: 'value',
      },
      series: [{
        name: config.seriesName || '数据',
        type: 'bar',
        data: data.values,
        itemStyle: {
          color: config.color || '#5470c6',
        },
      }],
    };
  }

  static generateLineChart(data: ChartData, config: Partial<ChartConfig>) {
    return {
      title: {
        text: config.title || '折线图',
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
      },
      xAxis: {
        type: 'category',
        data: data.categories,
      },
      yAxis: {
        type: 'value',
      },
      series: [{
        name: config.seriesName || '数据',
        type: 'line',
        data: data.values,
        smooth: config.smooth || false,
        lineStyle: {
          color: config.color || '#91cc75',
        },
      }],
    };
  }

  static generatePieChart(data: ChartData, config: Partial<ChartConfig>) {
    const pieData = data.categories.map((name, index) => ({
      name,
      value: data.values[index],
    }));

    return {
      title: {
        text: config.title || '饼图',
        left: 'center',
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)',
      },
      series: [{
        name: config.seriesName || '数据',
        type: 'pie',
        radius: config.radius || '50%',
        data: pieData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      }],
    };
  }
}
```

## 🔧 后端开发指南

### 项目结构详解
```
backend/
├── gateway/               # API网关服务
│   ├── src/
│   │   ├── middleware/   # 中间件
│   │   ├── routes/       # 路由配置
│   │   └── app.ts        # 应用入口
│   └── package.json
├── services/             # 微服务
│   ├── user-service/     # 用户服务
│   ├── data-service/     # 数据服务
│   ├── chart-service/    # 图表服务
│   └── file-service/     # 文件服务
├── shared/               # 共享模块
│   ├── database/         # 数据库配置
│   ├── utils/            # 工具函数
│   ├── types/            # 类型定义
│   └── middleware/       # 共享中间件
└── config/               # 配置文件
```

### API 开发规范

#### 1. 控制器示例
```typescript
// services/data-service/src/controllers/DatasetController.ts
import { Request, Response } from 'express';
import { DatasetService } from '../services/DatasetService';
import { CreateDatasetDto, UpdateDatasetDto } from '../dto/dataset.dto';
import { validateDto } from '../utils/validation';

export class DatasetController {
  constructor(private datasetService: DatasetService) {}

  async getDatasets(req: Request, res: Response) {
    try {
      const userId = req.user?.id;
      const datasets = await this.datasetService.findByUserId(userId);
      
      res.json({
        success: true,
        data: datasets,
        message: '获取数据集列表成功',
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: '获取数据集列表失败',
        error: error.message,
      });
    }
  }

  async createDataset(req: Request, res: Response) {
    try {
      const createDto = await validateDto(CreateDatasetDto, req.body);
      const userId = req.user?.id;
      
      const dataset = await this.datasetService.create({
        ...createDto,
        userId,
      });

      res.status(201).json({
        success: true,
        data: dataset,
        message: '创建数据集成功',
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: '创建数据集失败',
        error: error.message,
      });
    }
  }

  async updateDataset(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const updateDto = await validateDto(UpdateDatasetDto, req.body);
      const userId = req.user?.id;

      const dataset = await this.datasetService.update(id, updateDto, userId);

      res.json({
        success: true,
        data: dataset,
        message: '更新数据集成功',
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: '更新数据集失败',
        error: error.message,
      });
    }
  }

  async deleteDataset(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const userId = req.user?.id;

      await this.datasetService.delete(id, userId);

      res.json({
        success: true,
        message: '删除数据集成功',
      });
    } catch (error) {
      res.status(400).json({
        success: false,
        message: '删除数据集失败',
        error: error.message,
      });
    }
  }
}
```

#### 2. 服务层示例
```typescript
// services/data-service/src/services/DatasetService.ts
import { Repository } from 'typeorm';
import { Dataset } from '../entities/Dataset';
import { CreateDatasetDto, UpdateDatasetDto } from '../dto/dataset.dto';
import { FileService } from './FileService';
import { DataParserService } from './DataParserService';

export class DatasetService {
  constructor(
    private datasetRepository: Repository<Dataset>,
    private fileService: FileService,
    private dataParserService: DataParserService,
  ) {}

  async findByUserId(userId: string): Promise<Dataset[]> {
    return this.datasetRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  async create(createDto: CreateDatasetDto & { userId: string }): Promise<Dataset> {
    // 解析文件数据
    const fileData = await this.fileService.readFile(createDto.filePath);
    const parsedData = await this.dataParserService.parse(
      fileData,
      createDto.fileType,
    );

    // 创建数据集
    const dataset = this.datasetRepository.create({
      ...createDto,
      columnsInfo: parsedData.columns,
      rowCount: parsedData.rowCount,
    });

    return this.datasetRepository.save(dataset);
  }

  async update(
    id: string,
    updateDto: UpdateDatasetDto,
    userId: string,
  ): Promise<Dataset> {
    const dataset = await this.datasetRepository.findOne({
      where: { id, userId },
    });

    if (!dataset) {
      throw new Error('数据集不存在或无权限访问');
    }

    Object.assign(dataset, updateDto);
    return this.datasetRepository.save(dataset);
  }

  async delete(id: string, userId: string): Promise<void> {
    const dataset = await this.datasetRepository.findOne({
      where: { id, userId },
    });

    if (!dataset) {
      throw new Error('数据集不存在或无权限访问');
    }

    // 删除关联文件
    if (dataset.filePath) {
      await this.fileService.deleteFile(dataset.filePath);
    }

    await this.datasetRepository.remove(dataset);
  }
}
```

#### 3. 数据模型定义
```typescript
// services/data-service/src/entities/Dataset.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { User } from './User';

@Entity('datasets')
export class Dataset {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 100 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ name: 'file_path', length: 500, nullable: true })
  filePath: string;

  @Column({ name: 'file_size', type: 'bigint', nullable: true })
  fileSize: number;

  @Column({ name: 'file_type', length: 20, nullable: true })
  fileType: string;

  @Column({ name: 'columns_info', type: 'jsonb', nullable: true })
  columnsInfo: any;

  @Column({ name: 'row_count', type: 'integer', default: 0 })
  rowCount: number;

  @Column({ name: 'user_id' })
  userId: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user: User;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
```

### 数据处理服务

#### 文件解析服务
```typescript
// services/data-service/src/services/DataParserService.ts
import * as XLSX from 'xlsx';
import * as Papa from 'papaparse';
import { FileType, ParsedData, ColumnInfo } from '../types/data';

export class DataParserService {
  async parse(fileBuffer: Buffer, fileType: FileType): Promise<ParsedData> {
    switch (fileType) {
      case 'excel':
        return this.parseExcel(fileBuffer);
      case 'csv':
        return this.parseCsv(fileBuffer);
      case 'json':
        return this.parseJson(fileBuffer);
      case 'tsv':
        return this.parseTsv(fileBuffer);
      default:
        throw new Error(`不支持的文件类型: ${fileType}`);
    }
  }

  private async parseExcel(fileBuffer: Buffer): Promise<ParsedData> {
    const workbook = XLSX.read(fileBuffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    
    if (jsonData.length === 0) {
      throw new Error('Excel文件为空');
    }

    const headers = jsonData[0] as string[];
    const rows = jsonData.slice(1);

    const columns = this.analyzeColumns(headers, rows);

    return {
      columns,
      rowCount: rows.length,
      data: rows,
    };
  }

  private async parseCsv(fileBuffer: Buffer): Promise<ParsedData> {
    const csvText = fileBuffer.toString('utf-8');
    
    return new Promise((resolve, reject) => {
      Papa.parse(csvText, {
        header: true,
        complete: (results) => {
          if (results.errors.length > 0) {
            reject(new Error(`CSV解析错误: ${results.errors[0].message}`));
            return;
          }

          const headers = results.meta.fields || [];
          const rows = results.data;

          const columns = this.analyzeColumns(headers, rows);

          resolve({
            columns,
            rowCount: rows.length,
            data: rows,
          });
        },
        error: (error) => {
          reject(new Error(`CSV解析失败: ${error.message}`));
        },
      });
    });
  }

  private analyzeColumns(headers: string[], rows: any[]): ColumnInfo[] {
    return headers.map((header, index) => {
      const values = rows.map(row => row[index] || row[header]).filter(v => v != null);
      const dataType = this.detectDataType(values);
      
      return {
        name: header,
        dataType,
        nullable: values.length < rows.length,
        unique: new Set(values).size === values.length,
        sampleValues: values.slice(0, 10),
      };
    });
  }

  private detectDataType(values: any[]): string {
    if (values.length === 0) return 'string';

    const numericCount = values.filter(v => !isNaN(Number(v))).length;
    const dateCount = values.filter(v => !isNaN(Date.parse(v))).length;

    if (numericCount / values.length > 0.8) {
      return 'number';
    }
    
    if (dateCount / values.length > 0.8) {
      return 'date';
    }

    return 'string';
  }
}
```

---

## 🔄 开发工作流程

### Git 工作流
```bash
# 1. 创建功能分支
git checkout -b feature/chart-editor

# 2. 开发过程中定期提交
git add .
git commit -m "feat: 添加图表编辑器基础组件"

# 3. 推送到远程分支
git push origin feature/chart-editor

# 4. 创建 Pull Request
# 在 GitHub/GitLab 上创建 PR，等待代码审查

# 5. 合并到主分支
git checkout main
git pull origin main
git merge feature/chart-editor
git push origin main
```

### 代码审查清单
- [ ] 代码符合项目规范
- [ ] 类型定义完整
- [ ] 单元测试覆盖
- [ ] 性能考虑
- [ ] 安全性检查
- [ ] 文档更新

### 测试策略
```bash
# 单元测试
npm run test:unit

# 集成测试
npm run test:integration

# 端到端测试
npm run test:e2e

# 性能测试
npm run test:performance
```

---

*本技术实施指南将随着项目开发进展持续更新和完善*
